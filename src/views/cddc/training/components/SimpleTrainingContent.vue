<template>
  <div class="simple-training-content">
    <!-- 摄像头画面 -->
    <div class="camera-views">
      <CameraView
        :show-status-tag="true"
        :status-result="realTimeData?.operationLog?.result"
        @connected="handleCameraConnected"
        @disconnected="handleCameraDisconnected"
        @error="handleCameraError"
      />
      <div class="line"></div>
      <TeachingView :point-data="pointData" :diagram-type="projectDiagramType" />
    </div>
    
    <!-- 训练实时数据 -->
    <div class="flex gap-5 training-data">
      <div class="section-grid">
        <OperationLog
          ref="operationLogRef"
          class="flex-1 w-0"
          :detailId="trainId"
          :data="realTimeData"
          style="flex: 3"
        />
        <LiveData class="flex-1 w-0" :data="realTimeData" style="flex: 2" />
        <ProgressReport class="w-81" :data="trainProgress" style="flex: 1" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import CameraView from '/@/components/CameraView/index.vue';
import TeachingView from '/@/components/TeachingView/index.vue';
import OperationLog from './OperationLog.vue';
import LiveData from './LiveData.vue';
import ProgressReport from '/@/components/ClientPort/ProgressReport.vue';
import type { DiagramType } from '/@/components/TeachingView/index.vue';
import type { PointData, RealTimeData, TrainProgress } from '../data';

// Props 定义
interface Props {
  trainId: string;
  realTimeData: RealTimeData;
  trainProgress: TrainProgress;
  pointData: Array<PointData>;
  projectDiagramType?: DiagramType;
}

const props = defineProps<Props>();

// Emits 定义
interface Emits {
  (e: 'camera-connected'): void;
  (e: 'camera-disconnected'): void;
  (e: 'camera-error'): void;
}

const emit = defineEmits<Emits>();

// Refs
const operationLogRef = ref<any>(null);

// 摄像头事件处理
const handleCameraConnected = () => {
  emit('camera-connected');
};

const handleCameraDisconnected = () => {
  emit('camera-disconnected');
};

const handleCameraError = () => {
  emit('camera-error');
};

// 暴露方法给父组件
const clearOperationLog = () => {
  operationLogRef.value?.clearLocalLog();
};

defineExpose({
  clearOperationLog,
});
</script>

<style lang="less" scoped>
@import './style/progress-report.less';

.simple-training-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;

  .camera-views {
    display: flex;
    gap: 0.6vw;
    margin-bottom: 1.5vh;
    flex: 1;
    min-height: 0;
    background: url('@/assets/images/screen/train-camera-bg.png') no-repeat center / 100% 100%;
    padding: 1vh 1.5vw 3vh 1.4vw;
    position: relative;

    .line {
      width: 0.2vw;
      --ignore-dark-img: url('@/assets/images/screen/train-line.png');
      background: var(--ignore-dark-img) no-repeat center / 100% 100%;
      margin: 9vh 0 3vh 0;
    }
  }

  .training-data {
    flex-shrink: 0;

    .section-grid {
      display: flex;
      gap: 1vw;
      width: 100%;
    }
  }
}

@media screen and (max-width: 1024px) {
  .simple-training-content {
    .camera-views {
      gap: 0.5vw;
      margin-bottom: 1.2vh;
      padding: 0.8vh 1.2vw 2.5vh 1.2vw;

      .line {
        margin: 8vh 0 2.5vh 0;
      }
    }
  }
}
</style>
